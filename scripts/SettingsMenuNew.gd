extends Control

# Nová sekcia "Nastavenia" s krásnym Dark Templar dizajnom

@onready var main_panel: NinePatchRect = $MainPanel
@onready var title_label: Label = $MainPanel/ContentContainer/TitleContainer/TitleLabel
@onready var settings_panel: NinePatchRect = $MainPanel/ContentContainer/SettingsPanel
@onready var scroll_container: ScrollContainer = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer

# Volume sliders
@onready var master_volume_label: Label = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MasterVolumeContainer/VolumeLabel
@onready var master_volume_slider: HSlider = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MasterVolumeContainer/SliderContainer/VolumeSlider

@onready var music_volume_label: Label = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MusicVolumeContainer/VolumeLabel
@onready var music_volume_slider: HSlider = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/MusicVolumeContainer/SliderContainer/VolumeSlider

@onready var sfx_volume_label: Label = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/SFXVolumeContainer/VolumeLabel
@onready var sfx_volume_slider: HSlider = $MainPanel/ContentContainer/SettingsPanel/ScrollContainer/SettingsVBox/SFXVolumeContainer/SliderContainer/VolumeSlider



# Back button
@onready var back_button: TextureButton = $MainPanel/ContentContainer/ButtonContainer/BackButton
@onready var back_label: Label = $MainPanel/ContentContainer/ButtonContainer/BackButton/BackLabel

func _ready():
	print("Nová SettingsMenu sekcia načítaná")
	
	# Nastavenie sliderov
	setup_sliders()
	
	# Pripojenie signálov
	setup_signals()
	
	# Aplikovanie fontov a štýlov
	apply_styling()
	
	# Nastavenie obsahu
	setup_content()
	
	# Aktualizovanie labelov
	update_volume_labels()

	# Nastavenie mobilného scrollovania
	setup_mobile_scrolling()

	# Nastavenie fokusu
	if master_volume_slider:
		master_volume_slider.grab_focus()

func setup_sliders():
	"""Nastaví základné vlastnosti sliderov"""
	var sliders = [master_volume_slider, music_volume_slider, sfx_volume_slider]
	
	for slider in sliders:
		if slider:
			slider.min_value = 0.0
			slider.max_value = 1.0
			slider.step = 0.01
			slider.value = 1.0

func setup_signals():
	"""Pripojí signály pre ovládacie prvky"""
	if master_volume_slider:
		master_volume_slider.value_changed.connect(_on_master_volume_changed)
	
	if music_volume_slider:
		music_volume_slider.value_changed.connect(_on_music_volume_changed)
	
	if sfx_volume_slider:
		sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	
	if back_button:
		back_button.pressed.connect(_on_back_pressed)

func apply_styling():
	"""Aplikuje fonty a farby na všetky elementy"""

	if FontLoader:
		# Titulok sekcie
		if title_label:
			FontLoader.apply_font_style(title_label, "chapter_title")
			title_label.add_theme_color_override("font_color", Color("#D4AF37"))
			title_label.add_theme_font_size_override("font_size", 42)
			title_label.add_theme_constant_override("outline_size", 2)
			title_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

		# Volume labely
		var volume_labels = [master_volume_label, music_volume_label, sfx_volume_label]
		for label in volume_labels:
			if label:
				FontLoader.apply_font_style(label, "ui_elements")
				label.add_theme_color_override("font_color", Color("#F5F5DC"))
				label.add_theme_font_size_override("font_size", 24)
				label.add_theme_constant_override("outline_size", 1)
				label.add_theme_color_override("font_outline_color", Color("#2A1810"))



		# Tlačidlo späť
		if back_label:
			FontLoader.apply_font_style(back_label, "ui_elements")
			back_label.add_theme_color_override("font_color", Color("#D4AF37"))
			back_label.add_theme_font_size_override("font_size", 28)
			back_label.add_theme_constant_override("outline_size", 2)
			back_label.add_theme_color_override("font_outline_color", Color("#2A1810"))

func setup_content():
	"""Nastaví obsah sekcie"""
	if title_label:
		title_label.text = "NASTAVENIA"

	if back_label:
		back_label.text = "SPÄŤ"

func update_volume_labels():
	"""Aktualizuje texty volume labelov"""
	if master_volume_label and master_volume_slider:
		master_volume_label.text = "Hlavná hlasitosť: " + str(int(master_volume_slider.value * 100)) + "%"
	
	if music_volume_label and music_volume_slider:
		music_volume_label.text = "Hudba: " + str(int(music_volume_slider.value * 100)) + "%"
	
	if sfx_volume_label and sfx_volume_slider:
		sfx_volume_label.text = "Zvukové efekty: " + str(int(sfx_volume_slider.value * 100)) + "%"

func _on_master_volume_changed(value: float):
	"""Spracovanie zmeny hlavnej hlasitosti"""
	GameManager.update_setting("master_volume", value)
	update_volume_labels()

func _on_music_volume_changed(value: float):
	"""Spracovanie zmeny hlasitosti hudby"""
	GameManager.update_setting("music_volume", value)
	update_volume_labels()

func _on_sfx_volume_changed(value: float):
	"""Spracovanie zmeny hlasitosti efektov"""
	GameManager.update_setting("sfx_volume", value)
	update_volume_labels()

func setup_mobile_scrolling():
	"""Nastaví optimálne scrollovanie pre mobilné zariadenia"""
	if scroll_container:
		# Povoliť vertikálne scrollovanie s automatickým scrollbarom
		scroll_container.horizontal_scroll_mode = ScrollContainer.SCROLL_MODE_DISABLED
		scroll_container.vertical_scroll_mode = ScrollContainer.SCROLL_MODE_AUTO

		# Nastaviť deadzone pre lepšie dotyky
		scroll_container.scroll_deadzone = 0

		# Povoliť smooth scrolling
		scroll_container.follow_focus = true

		print("📱 Mobilné scrollovanie nastavené pre SettingsMenu")

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	print("Návrat do hlavného menu")
	AudioManager.play_menu_button_sound()
	GameManager.go_to_main_menu()

func _input(event):
	"""Spracovanie klávesových skratiek a mobilných dotykových gestúr"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()

	# Podpora pre mobilné scrollovanie pomocou swipe gestúr
	if event is InputEventScreenDrag and scroll_container:
		var drag_speed = 2.0
		scroll_container.scroll_vertical -= int(event.relative.y * drag_speed)
