# Van Helsing: Scenario Generated Assets

Tento projekt používa **Scenario API** na generovanie AI assetov pre hru <PERSON> He<PERSON>: Prekliate Dedičstvo.

## 🎨 Čo tento projekt robí

- **Generuje pozadia** pre hlavné menu v got<PERSON><PERSON> š<PERSON>ý<PERSON>
- **Vytvára portréty** postáv (Van <PERSON>, Dracula, atď.)
- **Produkuje UI elementy** (tlačidlá, rámčeky, dekorácie)
- **Automaticky ukladá** vygenerované assety do priečinka `assets/generated/`

## 🔧 Ako to funguje

### 1. Scenario API Integration
```gdscript
# API kľúč je nakonfigurovaný v ScenarioAPI.gd
const API_KEY = "api_ttVVbj1gnKRqUbrZytSiVgf4"
```

### 2. Generovanie assetov
Kliknite na **"Generovať Assety"** v hlavnom menu a systém automaticky:

1. **Pozadie hlavného menu** - Gotick<PERSON> hrad v noci s mesiacom
2. **<PERSON><PERSON><PERSON>** - Viktoriánsky lovec upírov
3. **Gotick<PERSON> tla<PERSON>** - Ozdobné rámčeky pre UI
4. **Logo titulku** - Štylizovaný nápis "Prekliate Dedičstvo"

### 3. Automatické ukladanie
Všetky vygenerované obrázky sa ukladajú do:
```
assets/generated/
├── main_menu_background.png
├── van_helsing_portrait.png
├── gothic_button.png
└── title_logo.png
```

## 🎮 Použitie

1. **Otvorte projekt** v Godot 4.4+
2. **Spustite hlavnú scénu** `scenes/MainMenu.tscn`
3. **Kliknite "Generovať Assety"** a počkajte na dokončenie
4. **Assety sa automaticky načítajú** do UI

## 📋 Funkcie

### ✅ Implementované
- [x] Scenario API integrácia
- [x] Automatické generovanie pozadí
- [x] Generovanie portrétov postáv
- [x] UI elementy (tlačidlá, rámčeky)
- [x] Automatické ukladanie assetov
- [x] Progress bar pre sledovanie pokroku
- [x] Error handling pre API chyby

### 🔄 Možné rozšírenia
- [ ] Generovanie assetov pre jednotlivé kapitoly
- [ ] Vytvorenie puzzle elementov
- [ ] Animované sprite sekvencje
- [ ] Zvukové efekty (ak Scenario podporuje audio)
- [ ] Batch generovanie viacerých variantov

## 🎨 Štýly a témy

Projekt používa **gotický/dark fantasy** štýl s týmito charakteristikami:

- **Farby**: Tmavé pozadia s zlatými akcentmi
- **Atmosféra**: Viktoriánska éra, upírske témy
- **Osvetlenie**: Dramatické tiene, mesačné svetlo
- **Detaily**: Ozdobné prvky, kamenná architektúra

## 🔗 API Dokumentácia

Scenario API endpoint:
```
https://api.scenario.com/v1/images/generate
```

Podporované parametre:
- `prompt`: Popis obrázka
- `width/height`: Rozlíšenie (720x1280 pre mobile)
- `guidance_scale`: Presnosť (7.5)
- `num_inference_steps`: Kvalita (30)

## 📱 Mobile Optimalizácia

Projekt je optimalizovaný pre **vertikálnu orientáciu** (720x1280):
- Responzívne UI elementy
- Touch-friendly tlačidlá
- Optimalizované rozlíšenie assetov

## 🚀 Spustenie

1. Otvorte `scenario/project.godot` v Godot
2. Spustite projekt (F5)
3. Kliknite "Generovať Assety"
4. Počkajte na dokončenie generovania
5. Assety sa automaticky načítajú

---

**Poznámka**: Tento projekt slúži ako demo/test pre Scenario API integráciu. Vygenerované assety môžete použiť v hlavnom projekte Van Helsing: Prekliate Dedičstvo.
