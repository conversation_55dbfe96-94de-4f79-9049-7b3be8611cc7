[gd_scene load_steps=3 format=3 uid="uid://bx8vn7qkqxqxq"]

[ext_resource type="Script" uid="uid://dl1a2ateu1dmw" path="res://scripts/MainMenu.gd" id="1_main"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.1, 0.05, 0.15, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.8, 0.6, 0.2, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 6

[node name="ColorRect" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.05, 0.02, 0.1, 0.8)

[node name="TitleContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -500.0
offset_right = 300.0
offset_bottom = -400.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="TitleContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 3
theme_override_font_sizes/font_size = 32
text = "Van Helsing: Prekliate Dedičstvo"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="GenerateButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 60)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 1, 0.8, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(1, 0.9, 0.7, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 20
theme_override_styles/normal = SubResource("StyleBoxFlat_1")
text = "Generovať Assety"

[node name="Spacer1" type="Control" parent="ButtonContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="NovaHraButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 18
text = "Nová Hra"

[node name="KapitolyButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 18
text = "Kapitoly"

[node name="NastaveniaButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 18
text = "Nastavenia"

[node name="OHreButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
theme_override_colors/font_hover_color = Color(1, 0.9, 0.7, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 18
text = "O Hre"

[node name="AssetGeneratorButton" type="Button" parent="ButtonContainer"]
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
theme_override_colors/font_hover_color = Color(0.8, 1, 0.7, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_colors/font_color = Color(0.7, 0.9, 0.6, 1)
theme_override_constants/outline_size = 2
theme_override_font_sizes/font_size = 18
text = "Asset Generator"

[node name="StatusLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -300.0
offset_top = -100.0
offset_right = 300.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(0.8, 0.7, 0.5, 1)
theme_override_colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
theme_override_constants/outline_size = 1
theme_override_font_sizes/font_size = 16
text = "Pripravený na generovanie assetov"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 0
theme_override_colors/font_color = Color(0.9, 0.8, 0.6, 1)
show_percentage = false
