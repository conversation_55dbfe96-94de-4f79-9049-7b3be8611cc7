[folding]

node_unfolds=[NodePath("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("ColorRect"), PackedStringArray("Layout"), NodePath("TitleContainer"), PackedStringArray("Layout"), NodePath("TitleContainer/TitleLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer"), PackedStringArray("Layout"), NodePath("ButtonContainer/GenerateButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer/Spacer1"), PackedStringArray("Layout"), NodePath("ButtonContainer/NovaHraButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer/KapitolyButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer/NastaveniaButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer/OHreButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ButtonContainer/AssetGeneratorButton"), PackedStringArray("Layout", "Theme Overrides"), NodePath("StatusLabel"), PackedStringArray("Layout", "Theme Overrides"), NodePath("ProgressBar"), PackedStringArray("Layout", "Theme Overrides")]
resource_unfolds=["res://scenes/MainMenu.tscn::StyleBoxFlat_1", PackedStringArray("Resource", "Border Width", "Border", "Corner Radius")]
nodes_folded=[]
