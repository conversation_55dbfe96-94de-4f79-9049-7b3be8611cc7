extends Node

# Scenario API Configuration
const API_KEY = "api_ttVVbj1gnKRqUbrZytSiVgf4"
const BASE_URL = "https://api.scenario.com/v1"

# HTTP Request node
var http_request: HTTPRequest

# Signals
signal image_generated(image_data: PackedByteArray, filename: String)
signal generation_failed(error_message: String)

func _ready():
	# Create HTTP request node
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_request_completed)

func generate_image(prompt: String, filename: String = "", style: String = "gothic"):
	"""
	Generate image using Scenario API
	
	Args:
		prompt: Description of what to generate
		filename: Optional filename for saving
		style: Art style (gothic, dark, medieval, etc.)
	"""
	
	if filename.is_empty():
		filename = "generated_" + str(Time.get_unix_time_from_system()) + ".png"
	
	# Enhanced prompt with gothic style
	var enhanced_prompt = _enhance_prompt_for_gothic_style(prompt, style)
	
	# Prepare request data
	var request_data = {
		"prompt": enhanced_prompt,
		"width": 720,
		"height": 1280,
		"num_images": 1,
		"guidance_scale": 7.5,
		"num_inference_steps": 30,
		"seed": -1
	}
	
	# Convert to JSON
	var json_string = JSON.stringify(request_data)
	
	# Prepare headers
	var headers = [
		"Content-Type: application/json",
		"Authorization: Bearer " + API_KEY
	]
	
	print("🎨 Generujem obrázok: ", enhanced_prompt)
	print("📁 Súbor: ", filename)
	
	# Store filename for later use
	http_request.set_meta("filename", filename)
	
	# Make request
	var error = http_request.request(
		BASE_URL + "/images/generate",
		headers,
		HTTPClient.METHOD_POST,
		json_string
	)
	
	if error != OK:
		emit_signal("generation_failed", "Chyba pri odosielaní požiadavky: " + str(error))

func _enhance_prompt_for_gothic_style(prompt: String, style: String) -> String:
	"""
	Enhance prompt with gothic/Van Helsing style elements
	"""
	var style_prefixes = {
		"gothic": "Gothic dark fantasy, Victorian era, dramatic lighting, ornate details, ",
		"dark": "Dark atmospheric, moody shadows, mysterious ambiance, ",
		"medieval": "Medieval fantasy, ancient castle, stone architecture, ",
		"vampire": "Vampire hunter theme, Van Helsing style, gothic horror, ",
		"menu": "Game menu background, elegant UI design, dark fantasy theme, "
	}
	
	var prefix = style_prefixes.get(style, style_prefixes["gothic"])
	var suffix = ", high quality, detailed, 4K, cinematic lighting, professional game art"
	
	return prefix + prompt + suffix

func _on_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""
	Handle API response
	"""
	var filename = http_request.get_meta("filename", "unknown.png")
	
	if response_code != 200:
		var error_msg = "API chyba: " + str(response_code)
		if body.size() > 0:
			var response_text = body.get_string_from_utf8()
			error_msg += " - " + response_text
		
		print("❌ ", error_msg)
		emit_signal("generation_failed", error_msg)
		return
	
	# Parse JSON response
	var json = JSON.new()
	var parse_result = json.parse(body.get_string_from_utf8())
	
	if parse_result != OK:
		emit_signal("generation_failed", "Chyba pri parsovaní JSON odpovede")
		return
	
	var response_data = json.data
	
	# Check if images were generated
	if not response_data.has("images") or response_data["images"].size() == 0:
		emit_signal("generation_failed", "Žiadne obrázky neboli vygenerované")
		return
	
	# Get first image URL
	var image_url = response_data["images"][0]["url"]
	
	print("🖼️ Sťahujem obrázok z: ", image_url)
	
	# Download the image
	_download_image(image_url, filename)

func _download_image(url: String, filename: String):
	"""
	Download generated image from URL
	"""
	# Create new HTTP request for image download
	var image_request = HTTPRequest.new()
	add_child(image_request)
	
	# Connect signal with filename parameter
	image_request.request_completed.connect(_on_image_downloaded.bind(filename, image_request))
	
	# Download image
	var error = image_request.request(url)
	
	if error != OK:
		emit_signal("generation_failed", "Chyba pri sťahovaní obrázka: " + str(error))
		image_request.queue_free()

func _on_image_downloaded(filename: String, request_node: HTTPRequest, result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	"""
	Handle downloaded image
	"""
	# Clean up request node
	request_node.queue_free()
	
	if response_code != 200:
		emit_signal("generation_failed", "Chyba pri sťahovaní obrázka: " + str(response_code))
		return
	
	if body.size() == 0:
		emit_signal("generation_failed", "Prázdny obrázok")
		return
	
	print("✅ Obrázok úspešne vygenerovaný: ", filename)
	print("📊 Veľkosť: ", body.size(), " bytov")
	
	# Save image to assets folder
	_save_image_to_assets(body, filename)
	
	# Emit success signal
	emit_signal("image_generated", body, filename)

func _save_image_to_assets(image_data: PackedByteArray, filename: String):
	"""
	Save generated image to assets folder
	"""
	# Create assets directory if it doesn't exist
	if not DirAccess.dir_exists_absolute("res://assets/"):
		DirAccess.open("res://").make_dir("assets")
	
	if not DirAccess.dir_exists_absolute("res://assets/generated/"):
		DirAccess.open("res://assets/").make_dir("generated")
	
	# Save file
	var file_path = "res://assets/generated/" + filename
	var file = FileAccess.open(file_path, FileAccess.WRITE)
	
	if file:
		file.store_buffer(image_data)
		file.close()
		print("💾 Obrázok uložený: ", file_path)
	else:
		print("❌ Chyba pri ukladaní obrázka: ", file_path)

# Convenience methods for specific Van Helsing assets
func generate_main_menu_background():
	generate_image(
		"Dark gothic castle at night with full moon, misty atmosphere, Van Helsing style, main menu background",
		"main_menu_background.png",
		"menu"
	)

func generate_van_helsing_portrait():
	generate_image(
		"Van Helsing character portrait, Victorian vampire hunter, dark coat, crossbow, determined expression",
		"van_helsing_portrait.png",
		"vampire"
	)

func generate_gothic_button():
	generate_image(
		"Gothic ornate button frame, dark metal with intricate engravings, game UI element",
		"gothic_button.png",
		"gothic"
	)

func generate_title_logo():
	generate_image(
		"Prekliate Dedičstvo gothic title logo, ornate lettering, dark fantasy style, Slovak text",
		"title_logo.png",
		"gothic"
	)

# Additional Van Helsing specific generators
func generate_chapter_background(chapter_number: int):
	var prompts = {
		1: "Dark forest path at night, mysterious fog, Van Helsing walking with lantern",
		2: "Gothic cathedral interior, stained glass windows, candlelight",
		3: "Ancient cemetery with weathered tombstones, full moon overhead",
		4: "Vampire castle throne room, ornate gothic architecture",
		5: "Underground crypt with stone coffins, torch lighting",
		6: "Final battle scene, Van Helsing vs Dracula, dramatic lighting",
		7: "Peaceful dawn after victory, Van Helsing silhouette against sunrise"
	}

	var prompt = prompts.get(chapter_number, "Gothic dark fantasy scene")
	generate_image(prompt, "chapter_" + str(chapter_number) + "_background.png", "gothic")

func generate_character_portrait(character_name: String):
	var character_prompts = {
		"van_helsing": "Van Helsing vampire hunter, Victorian coat, crossbow, determined expression, dark background",
		"dracula": "Count Dracula vampire lord, pale skin, red eyes, black cape, menacing smile",
		"mina": "Mina Harker Victorian lady, elegant dress, worried expression, gothic atmosphere",
		"renfield": "Renfield mad servant, wild hair, torn clothes, crazed eyes",
		"lucy": "Lucy Westenra vampire victim, pale beauty, flowing white dress, tragic expression"
	}

	var prompt = character_prompts.get(character_name.to_lower(), "Gothic character portrait")
	generate_image(prompt, character_name.to_lower() + "_portrait.png", "vampire")

func generate_puzzle_element(puzzle_type: String):
	var puzzle_prompts = {
		"cipher": "Ancient parchment with mysterious symbols, gothic calligraphy, candlelight",
		"memory": "Ornate memory cards with gothic symbols, dark wooden table",
		"riddle": "Old leather-bound book with riddles, quill pen, ink bottle",
		"navigation": "Ancient map with gothic compass, treasure markings",
		"ritual": "Mystical ritual circle with candles, ancient symbols, dark atmosphere"
	}

	var prompt = puzzle_prompts.get(puzzle_type, "Gothic puzzle element")
	generate_image(prompt, puzzle_type + "_puzzle.png", "gothic")

func generate_ui_element(element_type: String):
	var ui_prompts = {
		"button_normal": "Gothic ornate button frame, dark metal with engravings, normal state",
		"button_hover": "Gothic ornate button frame, dark metal with engravings, glowing effect",
		"panel": "Gothic decorative panel, ornate borders, dark background",
		"frame": "Victorian ornate picture frame, dark wood with gold details",
		"divider": "Gothic decorative divider, ornate line with flourishes"
	}

	var prompt = ui_prompts.get(element_type, "Gothic UI element")
	generate_image(prompt, "ui_" + element_type + ".png", "gothic")
