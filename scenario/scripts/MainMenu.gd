extends Control

# UI References
@onready var background = $Background
@onready var title_label = $TitleContainer/TitleLabel
@onready var generate_button = $ButtonContainer/GenerateButton
@onready var nova_hra_button = $ButtonContainer/NovaHraButton
@onready var kapitoly_button = $ButtonContainer/KapitolyButton
@onready var nastavenia_button = $ButtonContainer/NastaveniaButton
@onready var o_hre_button = $ButtonContainer/OHreButton
@onready var asset_generator_button = $ButtonContainer/AssetGeneratorButton
@onready var status_label = $StatusLabel
@onready var progress_bar = $ProgressBar

# Generation state
var assets_generated = false
var generation_in_progress = false

func _ready():
	setup_ui()
	connect_signals()
	check_existing_assets()

func setup_ui():
	"""Setup initial UI state"""
	title_label.text = "Van Helsing: Prekliate Dedičstvo"
	status_label.text = "Pripravený na generovanie assetov"
	progress_bar.visible = false
	
	# Set button texts
	generate_button.text = "Generovať Assety"
	nova_hra_button.text = "Nová Hra"
	kapitoly_button.text = "Kapitoly"
	nastavenia_button.text = "Nastavenia"
	o_hre_button.text = "O Hre"
	asset_generator_button.text = "Asset Generator"
	
	# Initially disable game buttons
	set_game_buttons_enabled(false)

func connect_signals():
	"""Connect button signals and API signals"""
	generate_button.pressed.connect(_on_generate_pressed)
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)
	asset_generator_button.pressed.connect(_on_asset_generator_pressed)
	
	# Connect Scenario API signals
	ScenarioAPI.image_generated.connect(_on_image_generated)
	ScenarioAPI.generation_failed.connect(_on_generation_failed)

func check_existing_assets():
	"""Check if assets are already generated"""
	var assets_dir = "res://assets/generated/"
	
	if DirAccess.dir_exists_absolute(assets_dir):
		var dir = DirAccess.open(assets_dir)
		if dir:
			var files = []
			dir.list_dir_begin()
			var file_name = dir.get_next()
			
			while file_name != "":
				if file_name.ends_with(".png"):
					files.append(file_name)
				file_name = dir.get_next()
			
			if files.size() > 0:
				assets_generated = true
				load_generated_assets()
				set_game_buttons_enabled(true)
				status_label.text = "Assety načítané (" + str(files.size()) + " súborov)"
				generate_button.text = "Regenerovať Assety"

func load_generated_assets():
	"""Load and apply generated assets"""
	var bg_path = "res://assets/generated/main_menu_background.png"
	
	if FileAccess.file_exists(bg_path):
		var texture = load(bg_path)
		if texture:
			background.texture = texture
			background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
			print("✅ Pozadie načítané: ", bg_path)

func set_game_buttons_enabled(enabled: bool):
	"""Enable/disable game navigation buttons"""
	nova_hra_button.disabled = not enabled
	kapitoly_button.disabled = not enabled
	nastavenia_button.disabled = not enabled
	o_hre_button.disabled = not enabled

func _on_generate_pressed():
	"""Start asset generation process"""
	if generation_in_progress:
		return
	
	generation_in_progress = true
	generate_button.disabled = true
	progress_bar.visible = true
	progress_bar.value = 0
	
	status_label.text = "Spúšťam generovanie assetov..."
	
	# Start generation sequence
	_start_generation_sequence()

func _start_generation_sequence():
	"""Generate all required assets in sequence"""
	var assets_to_generate = [
		{
			"method": "generate_main_menu_background",
			"description": "Generujem pozadie hlavného menu..."
		},
		{
			"method": "generate_van_helsing_portrait", 
			"description": "Generujem portrét Van Helsinga..."
		},
		{
			"method": "generate_gothic_button",
			"description": "Generujem gotické tlačidlá..."
		},
		{
			"method": "generate_title_logo",
			"description": "Generujem logo titulku..."
		}
	]
	
	# Store generation queue
	set_meta("generation_queue", assets_to_generate)
	set_meta("current_generation", 0)
	
	# Start first generation
	_generate_next_asset()

func _generate_next_asset():
	"""Generate next asset in queue"""
	var queue = get_meta("generation_queue", [])
	var current = get_meta("current_generation", 0)
	
	if current >= queue.size():
		_on_all_assets_generated()
		return
	
	var asset_info = queue[current]
	status_label.text = asset_info["description"]
	
	# Update progress
	progress_bar.value = (float(current) / float(queue.size())) * 100
	
	# Call generation method
	ScenarioAPI.call(asset_info["method"])

func _on_image_generated(image_data: PackedByteArray, filename: String):
	"""Handle successful image generation"""
	print("🎨 Asset vygenerovaný: ", filename)
	
	# Move to next asset
	var current = get_meta("current_generation", 0)
	set_meta("current_generation", current + 1)
	
	# Continue with next asset
	await get_tree().create_timer(1.0).timeout  # Small delay
	_generate_next_asset()

func _on_generation_failed(error_message: String):
	"""Handle generation failure"""
	print("❌ Chyba pri generovaní: ", error_message)
	status_label.text = "Chyba: " + error_message
	
	generation_in_progress = false
	generate_button.disabled = false
	progress_bar.visible = false

func _on_all_assets_generated():
	"""Called when all assets are generated"""
	print("✅ Všetky assety vygenerované!")
	
	assets_generated = true
	generation_in_progress = false
	
	status_label.text = "Všetky assety úspešne vygenerované!"
	progress_bar.value = 100
	
	# Load generated assets
	load_generated_assets()
	
	# Enable game buttons
	set_game_buttons_enabled(true)
	
	# Update generate button
	generate_button.text = "Regenerovať Assety"
	generate_button.disabled = false
	
	# Hide progress bar after delay
	await get_tree().create_timer(2.0).timeout
	progress_bar.visible = false

# Game navigation functions
func _on_nova_hra_pressed():
	status_label.text = "Spúšťam novú hru..."
	print("🎮 Nová hra")

func _on_kapitoly_pressed():
	status_label.text = "Otváram výber kapitol..."
	print("📚 Kapitoly")

func _on_nastavenia_pressed():
	status_label.text = "Otváram nastavenia..."
	print("⚙️ Nastavenia")

func _on_o_hre_pressed():
	status_label.text = "Otváram informácie o hre..."
	print("ℹ️ O hre")

func _on_asset_generator_pressed():
	status_label.text = "Otváram Asset Generator..."
	get_tree().change_scene_to_file("res://scenes/AssetGenerator.tscn")

# Custom asset generation functions
func generate_custom_asset():
	"""Generate custom asset with user input"""
	# This could be expanded to allow custom prompts
	var custom_prompt = "Gothic vampire castle interior, dark atmosphere, candlelight"
	ScenarioAPI.generate_image(custom_prompt, "custom_asset.png", "gothic")

func regenerate_specific_asset(asset_type: String):
	"""Regenerate specific asset type"""
	match asset_type:
		"background":
			ScenarioAPI.generate_main_menu_background()
		"portrait":
			ScenarioAPI.generate_van_helsing_portrait()
		"button":
			ScenarioAPI.generate_gothic_button()
		"logo":
			ScenarioAPI.generate_title_logo()
		_:
			print("❌ Neznámy typ assetu: ", asset_type)
