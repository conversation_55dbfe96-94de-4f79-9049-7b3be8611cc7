# 🎨 Van Helsing: Scenario API Project

Kompletný projekt pre generovanie AI assetov pomocou **Scenario API** pre hru Van Helsing: Prekliate Dedičstvo.

## 📁 Štruktúra Projektu

```
scenario/
├── project.godot              # Godot projekt konfigurácia
├── icon.svg                   # Ikona projektu
├── README.md                  # Základná dokumentácia
├── SCENARIO_PROJECT_GUIDE.md  # Tento súbor
├── scripts/
│   ├── ScenarioAPI.gd         # API komunikácia s Scenario
│   ├── MainMenu.gd            # Hlavné menu s generovaním
│   └── AssetGenerator.gd      # Pokročilý asset generator
├── scenes/
│   ├── MainMenu.tscn          # Hlavná scéna
│   └── AssetGenerator.tscn    # Asset generator scéna
└── assets/
    └── generated/             # Vygenerované assety
```

## 🔧 Nastavenie a Spustenie

### 1. Otvorenie Projektu
```bash
# Otvorte Godot 4.4+
# File -> Open Project
# Vyberte: scenario/project.godot
```

### 2. API Kľúč
API kľúč je už nakonfigurovaný v `ScenarioAPI.gd`:
```gdscript
const API_KEY = "api_ttVVbj1gnKRqUbrZytSiVgf4"
```

### 3. Spustenie
- Stlačte **F5** alebo kliknite **Play**
- Vyberte `MainMenu.tscn` ako hlavnú scénu

## 🎮 Používanie

### Základné Generovanie (MainMenu)
1. **Spustite projekt**
2. **Kliknite "Generovať Assety"**
3. **Počkajte na dokončenie** (progress bar)
4. **Assety sa automaticky načítajú**

### Pokročilé Generovanie (Asset Generator)
1. **Kliknite "Asset Generator"**
2. **Vyberte kategóriu** (Pozadia, Portréty, UI, Puzzle)
3. **Vyberte typ** assetu
4. **Voliteľne zadajte vlastný prompt**
5. **Kliknite "Generovať Asset"**
6. **Pozrite si náhľad** a zoznam assetov

## 🎨 Typy Assetov

### 📷 Pozadia
- **Hlavné menu**: Gotický hrad v noci
- **Kapitola 1-7**: Špecifické pozadia pre každú kapitolu

### 👤 Portréty Postáv
- **Van Helsing**: Lovec upírov s kuše
- **Dracula**: Upírsky lord s červenými očami
- **Mina**: Viktoriánska dáma
- **Renfield**: Šialený sluha
- **Lucy**: Upírska obeť

### 🖼️ UI Elementy
- **Tlačidlá**: Normálne a hover stavy
- **Panely**: Dekoratívne pozadia
- **Rámčeky**: Ozdobné okraje
- **Oddeľovače**: Gotické línie

### 🧩 Puzzle Elementy
- **Šifry**: Staré pergameny so symbolmi
- **Pamäť**: Gotické karty
- **Hádanky**: Staré knihy
- **Navigácia**: Mapy s kompasom
- **Rituály**: Mystické kruhy

## 🔄 API Funkcie

### Základné Generovanie
```gdscript
# Pozadie hlavného menu
ScenarioAPI.generate_main_menu_background()

# Portrét postavy
ScenarioAPI.generate_character_portrait("van_helsing")

# UI element
ScenarioAPI.generate_ui_element("button_normal")

# Vlastný prompt
ScenarioAPI.generate_image("custom prompt", "filename.png", "gothic")
```

### Batch Generovanie
```gdscript
# Všetky pozadia
generate_all_backgrounds()

# Všetky portréty
generate_all_portraits()

# Všetky UI elementy
generate_all_ui_elements()
```

## 📊 Technické Detaily

### API Parametre
- **Rozlíšenie**: 720x1280 (mobile portrait)
- **Guidance Scale**: 7.5
- **Inference Steps**: 30
- **Štýl**: Gothic/Dark Fantasy

### Automatické Ukladanie
- **Priečinok**: `res://assets/generated/`
- **Formát**: PNG
- **Názvy**: Automatické podľa typu

### Error Handling
- **API chyby**: Zobrazenie v status labeli
- **Sieťové problémy**: Retry mechanizmus
- **Neplatné odpovede**: Graceful handling

## 🎯 Použitie v Hlavnom Projekte

### Kopírovanie Assetov
```bash
# Skopírujte vygenerované assety do hlavného projektu
cp scenario/assets/generated/* ../assets/scenario_generated/
```

### Integrácia do Scén
```gdscript
# Načítanie vygenerovaného pozadia
var bg_texture = load("res://assets/scenario_generated/main_menu_background.png")
background.texture = bg_texture
```

## 🚀 Rozšírenia

### Nové Typy Assetov
1. **Pridajte do categories** v `AssetGenerator.gd`
2. **Vytvorte generátor funkciu** v `ScenarioAPI.gd`
3. **Aktualizujte UI** v `AssetGenerator.tscn`

### Vlastné Štýly
```gdscript
# Pridajte nový štýl do _enhance_prompt_for_gothic_style()
"steampunk": "Steampunk gothic, brass gears, Victorian machinery, "
```

### Animované Assety
```gdscript
# Generovanie sprite sekvencií
func generate_animated_sprite(base_prompt: String, frames: int):
    for i in range(frames):
        var frame_prompt = base_prompt + ", frame " + str(i)
        generate_image(frame_prompt, "anim_frame_" + str(i) + ".png")
```

## 🔍 Debugging

### Konzola Výstupy
- `🎨 Generujem obrázok:` - Začiatok generovania
- `✅ Obrázok úspešne vygenerovaný:` - Úspech
- `❌ Chyba pri generovaní:` - Chyba

### Kontrola Assetov
```gdscript
# Manuálna kontrola súborov
var dir = DirAccess.open("res://assets/generated/")
dir.list_dir_begin()
var file = dir.get_next()
while file != "":
    print("Asset: ", file)
    file = dir.get_next()
```

## 📝 Poznámky

- **Internet pripojenie** je potrebné pre API volania
- **API limity** môžu ovplyvniť rýchlosť generovania
- **Kvalita assetov** závisí od kvality promptov
- **Ukladanie** je automatické do `assets/generated/`

---

**Tento projekt demonštruje integráciu Scenario API do Godot projektu pre automatické generovanie herných assetov v gotickom štýle Van Helsing témy.** 🎮✨
